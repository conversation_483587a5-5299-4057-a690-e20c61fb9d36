import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance.dart';
import '../../providers/maintenance_providers.dart';
import '../../providers/auth_providers.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

// Temporary static model for maintenance issues display
class StaticMaintenanceIssue {
  final String title;
  final String department;
  final DateTime reportedDate;
  final String priority;
  final String status;
  final Color statusColor;
  final String recurrence;

  const StaticMaintenanceIssue({
    required this.title,
    required this.department,
    required this.reportedDate,
    required this.priority,
    required this.status,
    required this.statusColor,
    required this.recurrence,
  });
}

class MaintenanceScreenV1 extends ConsumerStatefulWidget {
  final String? propertyId;

  const MaintenanceScreenV1({super.key, this.propertyId});

  @override
  ConsumerState<MaintenanceScreenV1> createState() => _MaintenanceScreenV1State();
}



class _MaintenanceScreenV1State extends ConsumerState<MaintenanceScreenV1>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);

    // Check RBAC permissions
    if (!userPermissions.canViewMaintenance) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Maintenance'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Access Denied',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You don\'t have permission to view maintenance data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Maintenance',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          if (userPermissions.canCreateMaintenance)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showCreateIssueDialog,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: [
            if (userPermissions.canCreateMaintenance)
              const Tab(text: 'Submit Issue'),
            const Tab(text: 'Issue Status'),
            const Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Offline Indicator
          const OfflineIndicator(),

          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                if (userPermissions.canCreateMaintenance)
                  SubmitIssueTab(propertyId: widget.propertyId),
                IssueStatusTab(propertyId: widget.propertyId),
                StatisticsTabV1(propertyId: widget.propertyId),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    final currentQuery = ref.read(maintenanceSearchQueryProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Issues'),
        content: TextField(
          autofocus: true,
          controller: TextEditingController(text: currentQuery),
          decoration: const InputDecoration(
            hintText: 'Search by title, description, or department...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            ref.read(maintenanceSearchQueryProvider.notifier).state = value;
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(maintenanceSearchQueryProvider.notifier).state = '';
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialogV1(),
    );
  }

  void _showCreateIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateIssueDialogV1(propertyId: widget.propertyId),
    );
  }
}

class SubmitIssueTab extends StatefulWidget {
  const SubmitIssueTab({super.key});

  @override
  State<SubmitIssueTab> createState() => _SubmitIssueTabState();
}

class _SubmitIssueTabState extends State<SubmitIssueTab> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _remarksController = TextEditingController();
  final _reportedByController = TextEditingController();
  
  String? _selectedDepartment;
  String? _selectedPriority = 'Medium - Needs attention soon';
  String? _selectedStatus = 'Open';
  DateTime _startDate = DateTime.now();
  DateTime? _expectedEndDate;
  bool _isRecurring = false;

  final List<String> _departments = [
    'Electricity',
    'Water',
    'Security',
    'Internet',
    'Generator',
    'CCTV',
    'Maintenance',
    'OTTs',
  ];

  final List<String> _priorities = [
    'Low - Not urgent',
    'Medium - Needs attention soon',
    'High - Urgent',
    'Critical - Immediate action required',
  ];

  final List<String> _statuses = [
    'Open',
    'In Progress',
    'Completed',
    'Resolved',
    'Closed',
    'On Hold',
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Submit an Issue',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Report a new maintenance issue or request',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Issue Title
                const Text(
                  'Issue Title',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    hintText: 'Brief description of the issue',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an issue title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Department
                const Text(
                  'Department',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedDepartment,
                  decoration: const InputDecoration(
                    hintText: 'Select a department',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _departments.map((department) {
                    return DropdownMenuItem(
                      value: department,
                      child: Text(department),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a department';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Date Row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Start Date',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _startDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                setState(() {
                                  _startDate = date;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    '${_startDate.day.toString().padLeft(2, '0')}-${_startDate.month.toString().padLeft(2, '0')}-${_startDate.year}',
                                  ),
                                  const Spacer(),
                                  const Icon(Icons.calendar_today, size: 20),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Expected End Date',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _expectedEndDate ?? _startDate.add(const Duration(days: 7)),
                                firstDate: _startDate,
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                setState(() {
                                  _expectedEndDate = date;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    _expectedEndDate != null
                                        ? '${_expectedEndDate!.day.toString().padLeft(2, '0')}-${_expectedEndDate!.month.toString().padLeft(2, '0')}-${_expectedEndDate!.year}'
                                        : 'dd-mm-yyyy',
                                    style: TextStyle(
                                      color: _expectedEndDate != null
                                          ? Colors.black
                                          : Colors.grey,
                                    ),
                                  ),
                                  const Spacer(),
                                  const Icon(Icons.calendar_today, size: 20),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Priority
                const Text(
                  'Priority',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedPriority,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _priorities.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Text(priority),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPriority = value;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Status
                const Text(
                  'Status',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _statuses.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Reported By
                const Text(
                  'Reported By',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _reportedByController,
                  decoration: const InputDecoration(
                    hintText: 'Your name',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Recurring Issue Checkbox
                Row(
                  children: [
                    Checkbox(
                      value: _isRecurring,
                      onChanged: (value) {
                        setState(() {
                          _isRecurring = value ?? false;
                        });
                      },
                    ),
                    const Text('Recurring Issue'),
                  ],
                ),
                const SizedBox(height: 20),

                // Remarks
                const Text(
                  'Remarks',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _remarksController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    hintText: 'Please provide detailed information about the issue',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // TODO: Submit the issue
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Issue submitted successfully!'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    child: const Text(
                      'Submit Issue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _remarksController.dispose();
    _reportedByController.dispose();
    super.dispose();
  }
}

// Issue Status Tab
class IssueStatusTab extends StatefulWidget {
  const IssueStatusTab({super.key});

  @override
  State<IssueStatusTab> createState() => _IssueStatusTabState();
}

class _IssueStatusTabState extends State<IssueStatusTab> {
  final List<StaticMaintenanceIssue> _issues = [
    StaticMaintenanceIssue(
      title: 'Invertor Maintenance',
      department: 'electricity',
      reportedDate: DateTime(2025, 5, 1),
      priority: 'Low',
      status: 'Resolved',
      statusColor: Colors.green,
      recurrence: 'Monthly',
    ),
    StaticMaintenanceIssue(
      title: 'CCTV Annual Maintenance',
      department: 'security',
      reportedDate: DateTime(2025, 6, 1),
      priority: 'Low',
      status: 'Resolved',
      statusColor: Colors.green,
      recurrence: 'Yearly',
    ),
    StaticMaintenanceIssue(
      title: 'ACs - Annual Cleaning and Maintenance',
      department: 'electricity',
      reportedDate: DateTime(2025, 5, 1),
      priority: 'Low',
      status: 'Closed',
      statusColor: Colors.grey,
      recurrence: 'Yearly',
    ),
    StaticMaintenanceIssue(
      title: 'Generator - Annual Preventive Maintenance',
      department: 'electricity',
      reportedDate: DateTime(2024, 11, 30),
      priority: 'Medium',
      status: 'Resolved',
      statusColor: Colors.green,
      recurrence: 'Yearly',
    ),
    StaticMaintenanceIssue(
      title: 'Water Tank Automation - Dry Run on 24th May 2025',
      department: 'water',
      reportedDate: DateTime(2025, 5, 24),
      priority: 'High',
      status: 'Resolved',
      statusColor: Colors.green,
      recurrence: 'One time',
    ),
    StaticMaintenanceIssue(
      title: 'Water Tank - Semi Annual Cleaning',
      department: 'water',
      reportedDate: DateTime(2025, 5, 16),
      priority: 'Medium',
      status: 'Resolved',
      statusColor: Colors.green,
      recurrence: 'Recurrent',
    ),
    StaticMaintenanceIssue(
      title: 'Lighting - All external lighting in and around the House are functional',
      department: 'electricity',
      reportedDate: DateTime(2025, 5, 1),
      priority: 'Low',
      status: 'Closed',
      statusColor: Colors.grey,
      recurrence: 'Recurrent',
    ),
    StaticMaintenanceIssue(
      title: 'Generator Automation',
      department: 'electricity',
      reportedDate: DateTime(2025, 5, 12),
      priority: 'Medium',
      status: 'Open',
      statusColor: Colors.orange,
      recurrence: 'One time',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.list_alt, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    'Maintenance Issues',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Text(
                'Track the status of reported issues',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Issues List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _issues.length,
            itemBuilder: (context, index) {
              final issue = _issues[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                child: InkWell(
                  onTap: () {
                    _showIssueDetails(context, issue);
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                issue.title,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: issue.statusColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                issue.status,
                                style: TextStyle(
                                  color: issue.statusColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              _getDepartmentIcon(issue.department),
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              issue.department,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _formatDate(issue.reportedDate.toIso8601String()),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getPriorityColor(issue.priority).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                issue.priority,
                                style: TextStyle(
                                  color: _getPriorityColor(issue.priority),
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                issue.recurrence,
                                style: TextStyle(
                                  color: Colors.blue[700],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            const Spacer(),
                            Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.visibility, size: 20),
                                  onPressed: () => _showIssueDetails(context, issue),
                                  tooltip: 'View',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.edit, size: 20),
                                  onPressed: () => _editIssue(context, issue),
                                  tooltip: 'Edit',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                                  onPressed: () => _deleteIssue(context, issue),
                                  tooltip: 'Delete',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  IconData _getDepartmentIcon(String department) {
    switch (department.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'security':
        return Icons.security;
      case 'internet':
        return Icons.wifi;
      case 'generator':
        return Icons.power;
      default:
        return Icons.build;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }

  void _showIssueDetails(BuildContext context, StaticMaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(issue.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Department: ${issue.department}'),
            const SizedBox(height: 8),
            Text('Priority: ${issue.priority}'),
            const SizedBox(height: 8),
            Text('Status: ${issue.status}'),
            const SizedBox(height: 8),
            Text('Recurrence: ${issue.recurrence}'),
            const SizedBox(height: 8),
            Text('Reported: ${_formatDate(issue.reportedDate.toIso8601String())}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editIssue(BuildContext context, StaticMaintenanceIssue issue) {
    // TODO: Navigate to edit issue screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit functionality coming soon')),
    );
  }

  void _deleteIssue(BuildContext context, StaticMaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Issue'),
        content: Text('Are you sure you want to delete "${issue.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _issues.remove(issue);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Issue deleted successfully')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

// Functions List Tab
class FunctionsListTab extends StatelessWidget {
  const FunctionsListTab({super.key});

  final List<MaintenanceFunction> _functions = const [
    MaintenanceFunction(
      name: 'Attendance - Office',
      subFunction: 'Staff Presence',
      department: 'Maintenance',
      input: 'Check-in/check-out logs',
      process: 'Compare vs shift schedule',
      output: 'Present/Absent',
      thresholdLimits: 'Max 1 absence/week',
      responsibleAgent: 'Office Incharge',
    ),
    MaintenanceFunction(
      name: 'CCTV',
      subFunction: 'Feed Uptime',
      department: 'CCTV',
      input: 'IP stream status',
      process: 'Ping every hour',
      output: 'Online/Offline',
      thresholdLimits: 'Max downtime: 2 hrs',
      responsibleAgent: 'CCTV Vendor',
    ),
    MaintenanceFunction(
      name: 'Electricity',
      subFunction: 'Consumption Tracker',
      department: 'Electricity',
      input: 'Energy meter data (kWh)',
      process: 'Calculate daily/monthly usage',
      output: 'kWh per day/month',
      thresholdLimits: 'Max daily: 10 kWh',
      responsibleAgent: 'Electrician',
    ),
    MaintenanceFunction(
      name: 'Generator',
      subFunction: 'Fuel Level Monitoring',
      department: 'Generator',
      input: 'Manual entry or sensor',
      process: 'Log/check daily',
      output: 'Fuel % or litres',
      thresholdLimits: 'Min: 20%',
      responsibleAgent: 'Site Security Incharge',
    ),
    MaintenanceFunction(
      name: 'Water',
      subFunction: 'Tank Level Monitoring',
      department: 'Water',
      input: 'Sensor reading (%, time)',
      process: 'Fetch from sensor/API',
      output: 'Current water level (%)',
      thresholdLimits: 'Min: 20%, Max: 80%',
      responsibleAgent: 'Facility Supervisor',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.functions, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    'Maintenance Functions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Text(
                'Operational functions and processes',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Functions List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _functions.length,
            itemBuilder: (context, index) {
              final function = _functions[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                child: ExpansionTile(
                  title: Text(
                    function.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        function.subFunction,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            _getDepartmentIcon(function.department),
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            function.department,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildDetailRow('Input', function.input),
                          _buildDetailRow('Process', function.process),
                          _buildDetailRow('Output', function.output),
                          _buildDetailRow('Threshold Limits', function.thresholdLimits),
                          _buildDetailRow('Responsible Agent', function.responsibleAgent),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDepartmentIcon(String department) {
    switch (department.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'security':
        return Icons.security;
      case 'internet':
        return Icons.wifi;
      case 'generator':
        return Icons.power;
      case 'cctv':
        return Icons.videocam;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.settings;
    }
  }
}

class MaintenanceFunction {
  final String name;
  final String subFunction;
  final String department;
  final String input;
  final String process;
  final String output;
  final String thresholdLimits;
  final String responsibleAgent;

  const MaintenanceFunction({
    required this.name,
    required this.subFunction,
    required this.department,
    required this.input,
    required this.process,
    required this.output,
    required this.thresholdLimits,
    required this.responsibleAgent,
  });
}

class IssueDetailsDialog extends StatelessWidget {
  final MaintenanceIssue issue;

  const IssueDetailsDialog({super.key, required this.issue});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Issue Details',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              issue.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            _buildDetailRow('Department', issue.departmentName),
            _buildDetailRow('Priority', issue.priority),
            _buildDetailRow('Status', issue.status),
            _buildDetailRow('Recurrence', issue.frequency ?? 'One-time'),
            _buildDetailRow(
              'Reported Date',
              _formatDate(issue.reportedDate ?? issue.createdAt),
            ),

            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day} ${_getMonthName(date.month)}, ${date.year}';
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }
}

// Statistics Tab V1
class StatisticsTabV1 extends ConsumerWidget {
  final String? propertyId;

  const StatisticsTabV1({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Center(
      child: Text('Statistics Tab - Coming Soon'),
    );
  }
}

// Filter Dialog V1
class FilterDialogV1 extends StatelessWidget {
  const FilterDialogV1({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Issues'),
      content: const Text('Filter functionality coming soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// Create Issue Dialog V1
class CreateIssueDialogV1 extends StatelessWidget {
  final String? propertyId;

  const CreateIssueDialogV1({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Issue'),
      content: const Text('Create issue functionality coming soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
