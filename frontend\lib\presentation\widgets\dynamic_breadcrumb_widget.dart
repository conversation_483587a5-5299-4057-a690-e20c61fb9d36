import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/breadcrumb_permission_provider.dart';

// Enhanced breadcrumb item with resolved names
class ResolvedBreadcrumbItem {
  final String path;
  final String originalPath;
  final String displayName;
  final int level;
  final Map<String, String> parameters;
  final Map<String, String> resolvedParameters;
  final bool accessible;

  const ResolvedBreadcrumbItem({
    required this.path,
    required this.originalPath,
    required this.displayName,
    required this.level,
    required this.parameters,
    required this.resolvedParameters,
    required this.accessible,
  });

  factory ResolvedBreadcrumbItem.fromJson(Map<String, dynamic> json) {
    return ResolvedBreadcrumbItem(
      path: json['path'] ?? '',
      originalPath: json['originalPath'] ?? '',
      displayName: json['displayName'] ?? '',
      level: json['level'] ?? 0,
      parameters: Map<String, String>.from(json['parameters'] ?? {}),
      resolvedParameters: Map<String, String>.from(json['resolvedParameters'] ?? {}),
      accessible: json['accessible'] ?? true,
    );
  }
}

// Dynamic breadcrumb service
class DynamicBreadcrumbService {
  final BreadcrumbPermissionService _permissionService = BreadcrumbPermissionService();

  Future<List<ResolvedBreadcrumbItem>> getResolvedBreadcrumbs({
    required String currentPath,
    required Map<String, String> parameters,
  }) async {
    try {
      // This would call the backend resolver service
      // Use the service method instead of accessing private _apiClient
      final breadcrumbs = await _permissionService.getBreadcrumbNavigation(
        path: currentPath,
        params: parameters,
      );

      // Convert BreadcrumbItem to ResolvedBreadcrumbItem
      return breadcrumbs.map((item) => ResolvedBreadcrumbItem(
        path: item.path,
        displayName: item.name,
        originalPath: item.path,
        parameters: parameters,
        accessible: item.accessible,
        level: item.level,
      )).toList();
    } catch (e) {
      print('Error getting resolved breadcrumbs: $e');
    }

    return [];
  }

  Future<List<String>> getBreadcrumbSuggestions(String query) async {
    try {
      // For now, return empty suggestions since we don't have a direct method
      // This could be implemented as a separate method in BreadcrumbPermissionService
      return [];
    } catch (e) {
      print('Error getting breadcrumb suggestions: $e');
    }

    return [];
  }
}

// Providers
final dynamicBreadcrumbServiceProvider = Provider<DynamicBreadcrumbService>((ref) {
  return DynamicBreadcrumbService();
});

final resolvedBreadcrumbsProvider = FutureProvider.family<List<ResolvedBreadcrumbItem>, ResolvedBreadcrumbRequest>((ref, request) async {
  final service = ref.watch(dynamicBreadcrumbServiceProvider);
  return await service.getResolvedBreadcrumbs(
    currentPath: request.path,
    parameters: request.parameters,
  );
});

// Dynamic breadcrumb widget
class DynamicBreadcrumbNavigation extends ConsumerWidget {
  final String currentPath;
  final Map<String, String> parameters;
  final TextStyle? textStyle;
  final TextStyle? linkStyle;
  final String separator;
  final bool showIcons;
  final double? maxWidth;

  const DynamicBreadcrumbNavigation({
    super.key,
    required this.currentPath,
    required this.parameters,
    this.textStyle,
    this.linkStyle,
    this.separator = ' › ',
    this.showIcons = true,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final request = ResolvedBreadcrumbRequest(path: currentPath, parameters: parameters);
    final breadcrumbsAsync = ref.watch(resolvedBreadcrumbsProvider(request));

    return breadcrumbsAsync.when(
      data: (breadcrumbs) => _buildBreadcrumbs(context, breadcrumbs),
      loading: () => _buildLoadingBreadcrumbs(),
      error: (error, stack) => _buildErrorBreadcrumbs(error.toString()),
    );
  }

  Widget _buildBreadcrumbs(BuildContext context, List<ResolvedBreadcrumbItem> breadcrumbs) {
    if (breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    final widgets = <Widget>[];

    for (int i = 0; i < breadcrumbs.length; i++) {
      final breadcrumb = breadcrumbs[i];
      final isLast = i == breadcrumbs.length - 1;

      // Add icon if enabled
      if (showIcons && i == 0) {
        widgets.add(
          Icon(
            _getIconForPath(breadcrumb.path),
            size: 16,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        );
        widgets.add(const SizedBox(width: 4));
      }

      // Add breadcrumb item
      if (isLast) {
        // Current page - not clickable
        widgets.add(
          _buildCurrentBreadcrumb(context, breadcrumb),
        );
      } else if (breadcrumb.accessible) {
        // Clickable breadcrumb
        widgets.add(
          _buildClickableBreadcrumb(context, breadcrumb),
        );
      } else {
        // Non-accessible breadcrumb (show but disabled)
        widgets.add(
          _buildDisabledBreadcrumb(context, breadcrumb),
        );
      }

      // Add separator
      if (!isLast) {
        widgets.add(
          Text(
            separator,
            style: textStyle ?? Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
            ),
          ),
        );
      }
    }

    Widget breadcrumbWidget = Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      children: widgets,
    );

    // Apply max width constraint if specified
    if (maxWidth != null) {
      breadcrumbWidget = ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth!),
        child: breadcrumbWidget,
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: breadcrumbWidget,
    );
  }

  Widget _buildCurrentBreadcrumb(BuildContext context, ResolvedBreadcrumbItem breadcrumb) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        _truncateText(breadcrumb.displayName, 30),
        style: textStyle ?? Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildClickableBreadcrumb(BuildContext context, ResolvedBreadcrumbItem breadcrumb) {
    return InkWell(
      onTap: () => _navigateTo(context, breadcrumb),
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Text(
          _truncateText(breadcrumb.displayName, 25),
          style: linkStyle ?? Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).primaryColor,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

  Widget _buildDisabledBreadcrumb(BuildContext context, ResolvedBreadcrumbItem breadcrumb) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Text(
        _truncateText(breadcrumb.displayName, 25),
        style: textStyle ?? Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildLoadingBreadcrumbs() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade400),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Loading navigation...',
            style: TextStyle(color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorBreadcrumbs(String error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, size: 16, color: Colors.red.shade600),
          const SizedBox(width: 8),
          Text(
            'Navigation error',
            style: TextStyle(color: Colors.red.shade600),
          ),
        ],
      ),
    );
  }

  IconData _getIconForPath(String path) {
    if (path.contains('/dashboard')) return Icons.dashboard;
    if (path.contains('/properties')) return Icons.home;
    if (path.contains('/security')) return Icons.security;
    if (path.contains('/electricity')) return Icons.electrical_services;
    if (path.contains('/water')) return Icons.water_drop;
    if (path.contains('/office')) return Icons.business;
    if (path.contains('/maintenance')) return Icons.build;
    if (path.contains('/users')) return Icons.people;
    return Icons.navigate_next;
  }

  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }

  void _navigateTo(BuildContext context, ResolvedBreadcrumbItem breadcrumb) {
    // Implement navigation logic based on your routing system
    // You might need to convert the breadcrumb path back to a route
    Navigator.pushNamed(context, _pathToRoute(breadcrumb.originalPath, breadcrumb.parameters));
  }

  String _pathToRoute(String path, Map<String, String> parameters) {
    // Convert breadcrumb path back to Flutter route
    // This is a simplified example - you'd need to implement based on your routing
    String route = path;
    parameters.forEach((key, value) {
      route = route.replaceAll('{$key}', value);
    });
    return route;
  }
}

// Breadcrumb search widget for quick navigation
class BreadcrumbSearchWidget extends ConsumerStatefulWidget {
  final Function(String)? onNavigate;

  const BreadcrumbSearchWidget({
    super.key,
    this.onNavigate,
  });

  @override
  ConsumerState<BreadcrumbSearchWidget> createState() => _BreadcrumbSearchWidgetState();
}

class _BreadcrumbSearchWidgetState extends ConsumerState<BreadcrumbSearchWidget> {
  final TextEditingController _controller = TextEditingController();
  List<String> _suggestions = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          controller: _controller,
          decoration: InputDecoration(
            hintText: 'Search pages...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _isLoading 
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onChanged: _onSearchChanged,
        ),
        if (_suggestions.isNotEmpty) ...[
          const SizedBox(height: 8),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                return ListTile(
                  dense: true,
                  title: Text(_suggestions[index]),
                  onTap: () => _onSuggestionTap(_suggestions[index]),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  void _onSearchChanged(String query) async {
    if (query.length < 2) {
      setState(() {
        _suggestions = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final service = ref.read(dynamicBreadcrumbServiceProvider);
      final suggestions = await service.getBreadcrumbSuggestions(query);
      
      setState(() {
        _suggestions = suggestions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _suggestions = [];
        _isLoading = false;
      });
    }
  }

  void _onSuggestionTap(String suggestion) {
    widget.onNavigate?.call(suggestion);
    setState(() {
      _suggestions = [];
    });
    _controller.clear();
  }
}

// Request model for resolved breadcrumbs
class ResolvedBreadcrumbRequest {
  final String path;
  final Map<String, String> parameters;

  const ResolvedBreadcrumbRequest({
    required this.path,
    required this.parameters,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ResolvedBreadcrumbRequest &&
          runtimeType == other.runtimeType &&
          path == other.path &&
          _mapEquals(parameters, other.parameters);

  @override
  int get hashCode => path.hashCode ^ parameters.hashCode;

  bool _mapEquals(Map<String, String> a, Map<String, String> b) {
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}
