// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardOverview _$DashboardOverviewFromJson(Map<String, dynamic> json) =>
    DashboardOverview(
      summary:
          DashboardSummary.fromJson(json['summary'] as Map<String, dynamic>),
      properties: (json['properties'] as List<dynamic>)
          .map((e) => PropertySummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentAlerts: (json['recentAlerts'] as List<dynamic>)
          .map((e) => Alert.fromJson(e as Map<String, dynamic>))
          .toList(),
      systemStatuses: (json['systemStatuses'] as List<dynamic>)
          .map((e) => SystemStatusOverview.fromJson(e as Map<String, dynamic>))
          .toList(),
      activities: (json['activities'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map<String, dynamic>))
          .toList(),
      statistics: DashboardStatistics.fromJson(
          json['statistics'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DashboardOverviewToJson(DashboardOverview instance) =>
    <String, dynamic>{
      'summary': instance.summary,
      'properties': instance.properties,
      'recentAlerts': instance.recentAlerts,
      'systemStatuses': instance.systemStatuses,
      'activities': instance.activities,
      'statistics': instance.statistics,
    };

DashboardSummary _$DashboardSummaryFromJson(Map<String, dynamic> json) =>
    DashboardSummary(
      totalProperties: (json['totalProperties'] as num).toInt(),
      activeProperties: (json['activeProperties'] as num).toInt(),
      totalAlerts: (json['totalAlerts'] as num).toInt(),
      criticalAlerts: (json['criticalAlerts'] as num).toInt(),
      systemHealth: (json['systemHealth'] as num).toDouble(),
    );

Map<String, dynamic> _$DashboardSummaryToJson(DashboardSummary instance) =>
    <String, dynamic>{
      'totalProperties': instance.totalProperties,
      'activeProperties': instance.activeProperties,
      'totalAlerts': instance.totalAlerts,
      'criticalAlerts': instance.criticalAlerts,
      'systemHealth': instance.systemHealth,
    };

PropertySummary _$PropertySummaryFromJson(Map<String, dynamic> json) =>
    PropertySummary(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      healthScore: (json['healthScore'] as num).toDouble(),
      alertCount: (json['alertCount'] as num).toInt(),
      lastUpdate: json['lastUpdate'] as String,
    );

Map<String, dynamic> _$PropertySummaryToJson(PropertySummary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'status': instance.status,
      'healthScore': instance.healthScore,
      'alertCount': instance.alertCount,
      'lastUpdate': instance.lastUpdate,
    };

SystemStatusOverview _$SystemStatusOverviewFromJson(
        Map<String, dynamic> json) =>
    SystemStatusOverview(
      systemType: json['systemType'] as String,
      operational: (json['operational'] as num).toInt(),
      warning: (json['warning'] as num).toInt(),
      critical: (json['critical'] as num).toInt(),
      offline: (json['offline'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$SystemStatusOverviewToJson(
        SystemStatusOverview instance) =>
    <String, dynamic>{
      'systemType': instance.systemType,
      'operational': instance.operational,
      'warning': instance.warning,
      'critical': instance.critical,
      'offline': instance.offline,
      'total': instance.total,
    };

DashboardStatistics _$DashboardStatisticsFromJson(Map<String, dynamic> json) =>
    DashboardStatistics(
      timeRange: json['timeRange'] as String,
      metrics:
          DashboardMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => TrendData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DashboardStatisticsToJson(
        DashboardStatistics instance) =>
    <String, dynamic>{
      'timeRange': instance.timeRange,
      'metrics': instance.metrics,
      'trends': instance.trends,
    };

DashboardMetrics _$DashboardMetricsFromJson(Map<String, dynamic> json) =>
    DashboardMetrics(
      uptime: (json['uptime'] as num).toDouble(),
      incidents: (json['incidents'] as num).toInt(),
      resolved: (json['resolved'] as num).toInt(),
      avgResponseTime: (json['avgResponseTime'] as num).toDouble(),
    );

Map<String, dynamic> _$DashboardMetricsToJson(DashboardMetrics instance) =>
    <String, dynamic>{
      'uptime': instance.uptime,
      'incidents': instance.incidents,
      'resolved': instance.resolved,
      'avgResponseTime': instance.avgResponseTime,
    };

TrendData _$TrendDataFromJson(Map<String, dynamic> json) => TrendData(
      date: json['date'] as String,
      value: (json['value'] as num).toDouble(),
    );

Map<String, dynamic> _$TrendDataToJson(TrendData instance) => <String, dynamic>{
      'date': instance.date,
      'value': instance.value,
    };

PropertyDetail _$PropertyDetailFromJson(Map<String, dynamic> json) =>
    PropertyDetail(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String,
      description: json['description'] as String?,
      totalUnits: (json['totalUnits'] as num?)?.toInt(),
      occupiedUnits: (json['occupiedUnits'] as num?)?.toInt(),
      monthlyRent: (json['monthlyRent'] as num?)?.toDouble(),
      propertyValue: (json['propertyValue'] as num?)?.toDouble(),
      managerId: json['managerId'] as String?,
      status: json['status'] as String,
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      coordinates: (json['coordinates'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      systemStatusSummaries: (json['systemStatuses'] as List<dynamic>)
          .map((e) => SystemStatusSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      settings: json['settings'] == null
          ? null
          : PropertySettings.fromJson(json['settings'] as Map<String, dynamic>),
      recentActivities: (json['recentActivities'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map<String, dynamic>))
          .toList(),
      statistics: PropertyStatistics.fromJson(
          json['statistics'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyDetailToJson(PropertyDetail instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'description': instance.description,
      'totalUnits': instance.totalUnits,
      'occupiedUnits': instance.occupiedUnits,
      'monthlyRent': instance.monthlyRent,
      'propertyValue': instance.propertyValue,
      'managerId': instance.managerId,
      'status': instance.status,
      'amenities': instance.amenities,
      'images': instance.images,
      'coordinates': instance.coordinates,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'systemStatuses': instance.systemStatusSummaries,
      'settings': instance.settings,
      'recentActivities': instance.recentActivities,
      'statistics': instance.statistics,
    };

SystemStatusSummary _$SystemStatusSummaryFromJson(Map<String, dynamic> json) =>
    SystemStatusSummary(
      systemType: json['systemType'] as String,
      status: json['status'] as String,
      description: json['description'] as String?,
      healthScore: (json['healthScore'] as num?)?.toDouble(),
      lastChecked: json['lastChecked'] as String,
    );

Map<String, dynamic> _$SystemStatusSummaryToJson(
        SystemStatusSummary instance) =>
    <String, dynamic>{
      'systemType': instance.systemType,
      'status': instance.status,
      'description': instance.description,
      'healthScore': instance.healthScore,
      'lastChecked': instance.lastChecked,
    };

PropertyStatistics _$PropertyStatisticsFromJson(Map<String, dynamic> json) =>
    PropertyStatistics(
      totalSystems: (json['totalSystems'] as num).toInt(),
      operationalSystems: (json['operationalSystems'] as num).toInt(),
      warningSystems: (json['warningSystems'] as num).toInt(),
      criticalSystems: (json['criticalSystems'] as num).toInt(),
      offlineSystems: (json['offlineSystems'] as num).toInt(),
      averageHealthScore: (json['averageHealthScore'] as num).toDouble(),
      uptime: (json['uptime'] as num).toDouble(),
      lastIncident: json['lastIncident'] as String?,
    );

Map<String, dynamic> _$PropertyStatisticsToJson(PropertyStatistics instance) =>
    <String, dynamic>{
      'totalSystems': instance.totalSystems,
      'operationalSystems': instance.operationalSystems,
      'warningSystems': instance.warningSystems,
      'criticalSystems': instance.criticalSystems,
      'offlineSystems': instance.offlineSystems,
      'averageHealthScore': instance.averageHealthScore,
      'uptime': instance.uptime,
      'lastIncident': instance.lastIncident,
    };
