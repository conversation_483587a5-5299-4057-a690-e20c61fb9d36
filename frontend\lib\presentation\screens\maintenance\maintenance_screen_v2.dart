import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance.dart';
import '../../providers/maintenance_providers.dart';
import '../../providers/auth_providers.dart';
import '../../widgets/enhanced_error_widget.dart';
import '../../widgets/retry_widget.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

class MaintenanceScreenV2 extends ConsumerStatefulWidget {
  final String? propertyId;

  const MaintenanceScreenV2({super.key, this.propertyId});

  @override
  ConsumerState<MaintenanceScreenV2> createState() => _MaintenanceScreenV2State();
}

class _MaintenanceScreenV2State extends ConsumerState<MaintenanceScreenV2>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    
    // Initialize tab controller based on user permissions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userPermissions = ref.read(userPermissionsProvider);
      final tabCount = userPermissions.canCreateMaintenance ? 3 : 2;
      _tabController = TabController(length: tabCount, vsync: this);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);
    
    // Check RBAC permissions
    if (!userPermissions.canViewMaintenance) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Maintenance'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Access Denied',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You don\'t have permission to view maintenance data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Initialize tab controller if not already done
    if (!_tabController.hasListeners) {
      final tabCount = userPermissions.canCreateMaintenance ? 3 : 2;
      _tabController = TabController(length: tabCount, vsync: this);
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Maintenance',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          if (userPermissions.canCreateMaintenance)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showCreateIssueDialog,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: [
            if (userPermissions.canCreateMaintenance)
              const Tab(text: 'Submit Issue'),
            const Tab(text: 'Issue Status'),
            const Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Offline Indicator
          const OfflineIndicator(),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                if (userPermissions.canCreateMaintenance)
                  SubmitIssueTabV2(propertyId: widget.propertyId),
                IssueStatusTabV2(propertyId: widget.propertyId),
                StatisticsTabV2(propertyId: widget.propertyId),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    final currentQuery = ref.read(maintenanceSearchQueryProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Issues'),
        content: TextField(
          autofocus: true,
          controller: TextEditingController(text: currentQuery),
          decoration: const InputDecoration(
            hintText: 'Search by title, description, or department...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            ref.read(maintenanceSearchQueryProvider.notifier).state = value;
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(maintenanceSearchQueryProvider.notifier).state = '';
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialogV2(),
    );
  }

  void _showCreateIssueDialog() {
    showDialog(
      context: context,
      isScrollControlled: true,
      builder: (context) => CreateIssueDialogV2(propertyId: widget.propertyId),
    );
  }
}

// Submit Issue Tab V2
class SubmitIssueTabV2 extends ConsumerStatefulWidget {
  final String? propertyId;

  const SubmitIssueTabV2({super.key, this.propertyId});

  @override
  ConsumerState<SubmitIssueTabV2> createState() => _SubmitIssueTabV2State();
}

class _SubmitIssueTabV2State extends ConsumerState<SubmitIssueTabV2> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String? _selectedDepartment;
  String? _selectedPriority;
  bool _isSubmitting = false;

  final List<String> _departments = [
    'ELECTRICITY',
    'WATER',
    'SECURITY',
    'INTERNET',
    'GENERAL',
  ];

  final List<String> _priorities = [
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Submit Maintenance Issue',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Report a new maintenance issue or request',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                
                // Issue Title
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Issue Title *',
                    hintText: 'Brief description of the issue',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter an issue title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Department
                DropdownButtonFormField<String>(
                  value: _selectedDepartment,
                  decoration: const InputDecoration(
                    labelText: 'Department *',
                    border: OutlineInputBorder(),
                  ),
                  items: _departments.map((department) {
                    return DropdownMenuItem(
                      value: department,
                      child: Text(department),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a department';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Priority
                DropdownButtonFormField<String>(
                  value: _selectedPriority,
                  decoration: const InputDecoration(
                    labelText: 'Priority *',
                    border: OutlineInputBorder(),
                  ),
                  items: _priorities.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Text(priority),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPriority = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a priority';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Detailed description of the issue',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please provide a description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isSubmitting ? null : _submitIssue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isSubmitting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Submit Issue',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _submitIssue() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final params = CreateMaintenanceParams(
        propertyId: widget.propertyId ?? '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority!,
        department: _selectedDepartment!,
      );

      final success = await ref.read(createMaintenanceIssueProvider(params).future);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Issue submitted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          
          // Clear form
          _titleController.clear();
          _descriptionController.clear();
          setState(() {
            _selectedDepartment = null;
            _selectedPriority = null;
          });
        }
      } else {
        throw Exception('Failed to submit issue');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit issue: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

// Issue Status Tab V2
class IssueStatusTabV2 extends ConsumerWidget {
  final String? propertyId;

  const IssueStatusTabV2({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userPermissions = ref.watch(userPermissionsProvider);
    final filteredIssuesAsyncValue = ref.watch(filteredMaintenanceIssuesProvider);

    // Check tab-level permissions
    final tabPermissions = ref.watch(breadcrumbPermissionsProvider('Maintenance > Issue Status'));
    if (!tabPermissions.canAccess) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Access Denied',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              tabPermissions.denialReason ?? 'Insufficient permissions',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter Bar
        _buildFilterBar(ref),

        // Issues List
        Expanded(
          child: filteredIssuesAsyncValue.when(
            data: (issues) => _buildIssuesList(context, ref, issues, userPermissions),
            loading: () => _buildLoadingState(),
            error: (error, stack) => _buildErrorState(context, ref, error),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterBar(WidgetRef ref) {
    final selectedStatus = ref.watch(maintenanceStatusFilterProvider);
    final selectedPriority = ref.watch(maintenancePriorityFilterProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Filter
          const Text('Status:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ['all', 'OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'].map((status) {
                final isSelected = selectedStatus == status;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(status == 'all' ? 'All' : status.replaceAll('_', ' ')),
                    selected: isSelected,
                    onSelected: (selected) {
                      ref.read(maintenanceStatusFilterProvider.notifier).state = status;
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
                    checkmarkColor: AppTheme.primaryColor,
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(height: 12),

          // Priority Filter
          const Text('Priority:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ['all', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].map((priority) {
                final isSelected = selectedPriority == priority;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(priority == 'all' ? 'All' : priority),
                    selected: isSelected,
                    onSelected: (selected) {
                      ref.read(maintenancePriorityFilterProvider.notifier).state = priority;
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: _getPriorityColor(priority).withValues(alpha: 0.2),
                    checkmarkColor: _getPriorityColor(priority),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIssuesList(BuildContext context, WidgetRef ref, List<MaintenanceIssue> issues, UserPermissions userPermissions) {
    if (issues.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(maintenanceIssuesProvider);
        ref.invalidate(maintenanceStatisticsProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: issues.length,
        itemBuilder: (context, index) {
          final issue = issues[index];

          // Apply property-based filtering
          if (!userPermissions.canAccessProperty(issue.propertyId)) {
            return const SizedBox.shrink();
          }

          return _buildDynamicIssueCard(context, ref, issue, userPermissions);
        },
      ),
    );
  }

  Widget _buildDynamicIssueCard(BuildContext context, WidgetRef ref, MaintenanceIssue issue, UserPermissions userPermissions) {
    final statusColor = _getStatusColor(issue.status);
    final priorityColor = _getPriorityColor(issue.priority);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _showIssueDetail(context, ref, issue),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Text(
                      issue.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      issue.status.replaceAll('_', ' '),
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Description
              Text(
                issue.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Details Row
              Row(
                children: [
                  // Department
                  Icon(
                    _getDepartmentIcon(issue.department),
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    issue.department,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Priority
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: priorityColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      issue.priority,
                      style: TextStyle(
                        color: priorityColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Date
                  Text(
                    _formatDate(issue.reportedDate),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),

              if (issue.assignedToName != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Assigned to: ${issue.assignedToName}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 5,
      itemBuilder: (context, index) => _buildIssueCardSkeleton(),
    );
  }

  Widget _buildIssueCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  width: 80,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 14,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              height: 14,
              width: 200,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  width: 60,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 40,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const Spacer(),
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return RetryWidget(
      onRetry: () async {
        ref.invalidate(maintenanceIssuesProvider);
      },
      child: const SizedBox.shrink(),
      errorBuilder: (error, retryCount, retry) => EnhancedErrorWidget(
        error: error,
        onRetry: retry,
        title: 'Failed to load maintenance issues',
        message: 'Please check your connection and try again.',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.build_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No maintenance issues found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Issues will appear here when reported',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showIssueDetail(BuildContext context, WidgetRef ref, MaintenanceIssue issue) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => IssueDetailSheet(issue: issue),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPEN':
        return Colors.orange;
      case 'IN_PROGRESS':
        return Colors.blue;
      case 'RESOLVED':
        return Colors.green;
      case 'CLOSED':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toUpperCase()) {
      case 'LOW':
        return Colors.green;
      case 'MEDIUM':
        return Colors.orange;
      case 'HIGH':
        return Colors.red;
      case 'CRITICAL':
        return Colors.red[900]!;
      default:
        return Colors.grey;
    }
  }

  IconData _getDepartmentIcon(String department) {
    switch (department.toUpperCase()) {
      case 'ELECTRICITY':
        return Icons.electrical_services;
      case 'WATER':
        return Icons.water_drop;
      case 'SECURITY':
        return Icons.security;
      case 'INTERNET':
        return Icons.wifi;
      case 'GENERAL':
        return Icons.build;
      default:
        return Icons.build;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays == 0) {
        return 'Today';
      } else if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return dateString;
    }
  }
}

// Statistics Tab V2
class StatisticsTabV2 extends ConsumerWidget {
  final String? propertyId;

  const StatisticsTabV2({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsyncValue = ref.watch(maintenanceStatisticsProvider(propertyId));

    return statisticsAsyncValue.when(
      data: (statistics) => _buildStatisticsContent(context, statistics),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildStatisticsContent(BuildContext context, MaintenanceStatistics statistics) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Cards
          Text(
            'Overview',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Issues',
                  statistics.totalIssues.toString(),
                  Icons.build,
                  AppTheme.infoColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Open Issues',
                  statistics.openIssues.toString(),
                  Icons.error_outline,
                  Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'In Progress',
                  statistics.inProgressIssues.toString(),
                  Icons.pending,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Resolved',
                  statistics.resolvedIssues.toString(),
                  Icons.check_circle,
                  AppTheme.successColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Resolution Rate
          Text(
            'Performance Metrics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Resolution Rate',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${statistics.resolutionRate.toStringAsFixed(1)}%'),
                      Text(
                        '${statistics.resolvedIssues + statistics.closedIssues}/${statistics.totalIssues} resolved',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: statistics.resolutionRate / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.successColor),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Average Resolution Time',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    statistics.averageResolutionTimeFormatted,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return EnhancedErrorWidget(
      error: error,
      onRetry: () {
        ref.invalidate(maintenanceStatisticsProvider);
      },
      title: 'Failed to load statistics',
      message: 'Please check your connection and try again.',
    );
  }
}

// Filter Dialog V2
class FilterDialogV2 extends ConsumerWidget {
  const FilterDialogV2({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStatus = ref.watch(maintenanceStatusFilterProvider);
    final selectedPriority = ref.watch(maintenancePriorityFilterProvider);
    final selectedDepartment = ref.watch(maintenanceDepartmentFilterProvider);

    return AlertDialog(
      title: const Text('Filter Issues'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Status:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'].map((status) {
              return FilterChip(
                label: Text(status == 'all' ? 'All' : status.replaceAll('_', ' ')),
                selected: selectedStatus == status,
                onSelected: (selected) {
                  ref.read(maintenanceStatusFilterProvider.notifier).state = status;
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          const Text('Priority:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].map((priority) {
              return FilterChip(
                label: Text(priority == 'all' ? 'All' : priority),
                selected: selectedPriority == priority,
                onSelected: (selected) {
                  ref.read(maintenancePriorityFilterProvider.notifier).state = priority;
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          const Text('Department:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'ELECTRICITY', 'WATER', 'SECURITY', 'INTERNET', 'GENERAL'].map((department) {
              return FilterChip(
                label: Text(department == 'all' ? 'All' : department),
                selected: selectedDepartment == department,
                onSelected: (selected) {
                  ref.read(maintenanceDepartmentFilterProvider.notifier).state = department;
                },
              );
            }).toList(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            ref.read(maintenanceStatusFilterProvider.notifier).state = 'all';
            ref.read(maintenancePriorityFilterProvider.notifier).state = 'all';
            ref.read(maintenanceDepartmentFilterProvider.notifier).state = 'all';
          },
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// Create Issue Dialog V2
class CreateIssueDialogV2 extends ConsumerStatefulWidget {
  final String? propertyId;

  const CreateIssueDialogV2({super.key, this.propertyId});

  @override
  ConsumerState<CreateIssueDialogV2> createState() => _CreateIssueDialogV2State();
}

class _CreateIssueDialogV2State extends ConsumerState<CreateIssueDialogV2> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedDepartment;
  String? _selectedPriority;
  final bool _isSubmitting = false;

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.95,
      minChildSize: 0.6,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'Create Issue',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Form
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Similar form fields as in SubmitIssueTabV2
                      // ... (form implementation)
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Issue Detail Sheet
class IssueDetailSheet extends StatelessWidget {
  final MaintenanceIssue issue;

  const IssueDetailSheet({super.key, required this.issue});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.95,
      minChildSize: 0.6,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      issue.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Issue details implementation
                    Text(
                      'Description',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(issue.description),

                    const SizedBox(height: 16),

                    // More details...
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
