import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/system.dart';
import '../../providers/system_providers.dart';
import '../../providers/auth_providers.dart';
import '../../widgets/enhanced_error_widget.dart';
import '../../widgets/retry_widget.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

class WaterManagementScreen extends ConsumerStatefulWidget {
  final String? propertyId;

  const WaterManagementScreen({super.key, this.propertyId});

  @override
  ConsumerState<WaterManagementScreen> createState() => _WaterManagementScreenState();
}

class _WaterManagementScreenState extends ConsumerState<WaterManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);
    
    // Check RBAC permissions
    if (!userPermissions.canViewWaterSystems) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Water Management'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Access Denied',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You don\'t have permission to view water management data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Water Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSystemSettings,
          ),
          if (userPermissions.canManageWaterSystems)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddSystemDialog,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Tank Levels'),
            Tab(text: 'Consumption'),
            Tab(text: 'Maintenance'),
            Tab(text: 'Quality'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Offline Indicator
          const OfflineIndicator(),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                TankLevelsTab(propertyId: widget.propertyId),
                ConsumptionTab(propertyId: widget.propertyId),
                WaterMaintenanceTab(propertyId: widget.propertyId),
                QualityTab(propertyId: widget.propertyId),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _refreshData() {
    ref.invalidate(waterSystemsProvider);
    ref.invalidate(waterConsumptionProvider);
    ref.invalidate(waterMaintenanceProvider);
    ref.invalidate(waterQualityProvider);
  }

  void _showSystemSettings() {
    showDialog(
      context: context,
      builder: (context) => WaterSystemSettingsDialog(propertyId: widget.propertyId),
    );
  }

  void _showAddSystemDialog() {
    showDialog(
      context: context,
      isScrollControlled: true,
      builder: (context) => AddWaterSystemDialog(propertyId: widget.propertyId),
    );
  }
}

// Tank Levels Tab
class TankLevelsTab extends ConsumerWidget {
  final String? propertyId;

  const TankLevelsTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final waterSystemsAsyncValue = ref.watch(waterSystemsProvider(propertyId ?? ''));

    return waterSystemsAsyncValue.when(
      data: (waterSystemsResponse) => _buildTankLevelsContent(context, ref, waterSystemsResponse),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildTankLevelsContent(BuildContext context, WidgetRef ref, WaterSystemsResponse response) {
    if (response.systems.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(waterSystemsProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            _buildSummaryCards(context, response.summary),
            
            const SizedBox(height: 24),
            
            // Tank Level Cards
            Text(
              'Tank Levels',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...response.systems.map((system) => _buildTankCard(context, system)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, WaterSystemsSummary summary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Capacity',
            '${summary.totalCapacity.toStringAsFixed(0)}L',
            Icons.water_drop,
            AppTheme.infoColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Current Level',
            '${summary.totalCurrentLevel.toStringAsFixed(0)}L',
            Icons.water,
            AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTankCard(BuildContext context, WaterSystem system) {
    final levelColor = _getLevelColor(system.levelPercentage);
    final pumpStatusColor = _getPumpStatusColor(system.pumpStatus);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    system.tankName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: pumpStatusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: pumpStatusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    system.pumpStatus,
                    style: TextStyle(
                      color: pumpStatusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Level Progress Bar
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Water Level',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '${system.levelPercentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: levelColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: system.levelPercentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(levelColor),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${system.currentLevel.toStringAsFixed(0)}L / ${system.capacity.toStringAsFixed(0)}L',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Additional Info
            Row(
              children: [
                if (system.flowRate != null) ...[
                  Icon(Icons.speed, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${system.flowRate!.toStringAsFixed(1)} L/min',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                if (system.pressure != null) ...[
                  Icon(Icons.compress, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${system.pressure!.toStringAsFixed(1)} PSI',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                if (system.quality != null) ...[
                  Icon(Icons.water_drop, size: 16, color: _getQualityColor(system.quality!)),
                  const SizedBox(width: 4),
                  Text(
                    system.quality!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getQualityColor(system.quality!),
                    ),
                  ),
                ],
              ],
            ),
            
            if (system.needsMaintenance) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Maintenance Required',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) => _buildTankCardSkeleton(),
    );
  }

  Widget _buildTankCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  width: 60,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 60,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return RetryWidget(
      onRetry: () async {
        ref.invalidate(waterSystemsProvider);
      },
      child: const SizedBox.shrink(),
      errorBuilder: (error, retryCount, retry) => EnhancedErrorWidget(
        error: error,
        onRetry: retry,
        title: 'Failed to load water systems',
        message: 'Please check your connection and try again.',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.water_drop_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No water systems found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Water systems will appear here when configured',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getLevelColor(double percentage) {
    if (percentage < 10) return Colors.red;
    if (percentage < 30) return Colors.orange;
    return Colors.green;
  }

  Color _getPumpStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ON':
        return Colors.green;
      case 'OFF':
        return Colors.grey;
      case 'MAINTENANCE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getQualityColor(String quality) {
    switch (quality.toUpperCase()) {
      case 'GOOD':
        return Colors.green;
      case 'FAIR':
        return Colors.orange;
      case 'POOR':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

// Water Maintenance Tab
class WaterMaintenanceTab extends ConsumerWidget {
  final String? propertyId;

  const WaterMaintenanceTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final maintenanceAsyncValue = ref.watch(waterMaintenanceProvider(propertyId ?? ''));

    return maintenanceAsyncValue.when(
      data: (maintenance) => _buildMaintenanceContent(context, ref, maintenance),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildMaintenanceContent(BuildContext context, WidgetRef ref, WaterMaintenance maintenance) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(waterMaintenanceProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Maintenance Summary
            _buildMaintenanceSummary(context, maintenance),

            const SizedBox(height: 24),

            // Upcoming Maintenance
            _buildUpcomingMaintenance(context, maintenance.upcomingMaintenance),

            const SizedBox(height: 24),

            // Maintenance History
            _buildMaintenanceHistory(context, maintenance.maintenanceHistory),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceSummary(BuildContext context, WaterMaintenance maintenance) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Maintenance Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildMaintenanceMetric(
                    'Overdue',
                    maintenance.overdueTasks.toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildMaintenanceMetric(
                    'Due Soon',
                    maintenance.dueSoonTasks.toString(),
                    Icons.schedule,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildMaintenanceMetric(
                    'Completed',
                    maintenance.completedTasks.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceMetric(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUpcomingMaintenance(BuildContext context, List<MaintenanceTask> upcomingTasks) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Upcoming Maintenance',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showScheduleMaintenanceDialog(context),
                  child: const Text('Schedule New'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (upcomingTasks.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    'No upcoming maintenance tasks',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              ...upcomingTasks.map((task) => _buildMaintenanceTaskCard(context, task)),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceTaskCard(BuildContext context, MaintenanceTask task) {
    final isOverdue = task.dueDate.isBefore(DateTime.now());
    final isDueSoon = task.dueDate.difference(DateTime.now()).inDays <= 7;

    Color statusColor = Colors.green;
    if (isOverdue) {
      statusColor = Colors.red;
    } else if (isDueSoon) {
      statusColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getMaintenanceIcon(task.type),
          color: statusColor,
        ),
        title: Text(task.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(task.description),
            const SizedBox(height: 4),
            Text(
              'Due: ${_formatDate(task.dueDate)}',
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'complete',
              child: Text('Mark Complete'),
            ),
            const PopupMenuItem(
              value: 'reschedule',
              child: Text('Reschedule'),
            ),
            const PopupMenuItem(
              value: 'details',
              child: Text('View Details'),
            ),
          ],
          onSelected: (value) => _handleMaintenanceAction(context, task, value),
        ),
      ),
    );
  }

  Widget _buildMaintenanceHistory(BuildContext context, List<MaintenanceRecord> history) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Maintenance History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            if (history.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    'No maintenance history available',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              ...history.take(5).map((record) => _buildMaintenanceHistoryItem(context, record)),

            if (history.length > 5)
              TextButton(
                onPressed: () => _showFullHistory(context, history),
                child: const Text('View All History'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceHistoryItem(BuildContext context, MaintenanceRecord record) {
    return ListTile(
      leading: Icon(
        _getMaintenanceIcon(record.type),
        color: Colors.green,
      ),
      title: Text(record.title),
      subtitle: Text('Completed: ${_formatDate(record.completedDate)}'),
      trailing: Text(
        record.cost != null ? '\$${record.cost!.toStringAsFixed(2)}' : '',
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return EnhancedErrorWidget(
      error: error,
      onRetry: () => ref.invalidate(waterMaintenanceProvider),
      title: 'Failed to load maintenance data',
      message: 'Please check your connection and try again.',
    );
  }

  IconData _getMaintenanceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'pump':
        return Icons.settings;
      case 'tank':
        return Icons.water_drop;
      case 'filter':
        return Icons.filter_alt;
      case 'pipe':
        return Icons.plumbing;
      default:
        return Icons.build;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showScheduleMaintenanceDialog(BuildContext context) {
    // Implementation for scheduling new maintenance
  }

  void _handleMaintenanceAction(BuildContext context, MaintenanceTask task, String action) {
    // Implementation for maintenance actions
  }

  void _showFullHistory(BuildContext context, List<MaintenanceRecord> history) {
    // Implementation for showing full history
  }
}

// Quality Tab
class QualityTab extends ConsumerWidget {
  final String? propertyId;

  const QualityTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final qualityAsyncValue = ref.watch(waterQualityProvider(propertyId ?? ''));

    return qualityAsyncValue.when(
      data: (quality) => _buildQualityContent(context, ref, quality),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildQualityContent(BuildContext context, WidgetRef ref, WaterQuality quality) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(waterQualityProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quality Overview
            _buildQualityOverview(context, quality),

            const SizedBox(height: 24),

            // Quality Parameters
            _buildQualityParameters(context, quality.parameters),

            const SizedBox(height: 24),

            // Quality History
            _buildQualityHistory(context, quality.history),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityOverview(BuildContext context, WaterQuality quality) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Water Quality Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildQualityMetric(
                    'Overall Rating',
                    quality.overallRating,
                    _getQualityColor(quality.overallRating),
                    Icons.water_drop,
                  ),
                ),
                Expanded(
                  child: _buildQualityMetric(
                    'Last Tested',
                    _formatDate(quality.lastTested),
                    Colors.grey[600]!,
                    Icons.schedule,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (quality.alerts.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Quality Alerts',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ...quality.alerts.map((alert) => Text(
                      '• $alert',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontSize: 12,
                      ),
                    )),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQualityMetric(String title, String value, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQualityParameters(BuildContext context, List<QualityParameter> parameters) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quality Parameters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            ...parameters.map((parameter) => _buildParameterItem(context, parameter)),
          ],
        ),
      ),
    );
  }

  Widget _buildParameterItem(BuildContext context, QualityParameter parameter) {
    final isWithinRange = parameter.value >= parameter.minValue && parameter.value <= parameter.maxValue;
    final statusColor = isWithinRange ? Colors.green : Colors.red;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  parameter.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                ),
                child: Text(
                  isWithinRange ? 'Normal' : 'Alert',
                  style: TextStyle(
                    color: statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            children: [
              Text(
                '${parameter.value.toStringAsFixed(2)} ${parameter.unit}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Range: ${parameter.minValue.toStringAsFixed(1)} - ${parameter.maxValue.toStringAsFixed(1)} ${parameter.unit}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          LinearProgressIndicator(
            value: (parameter.value - parameter.minValue) / (parameter.maxValue - parameter.minValue),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(statusColor),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityHistory(BuildContext context, List<QualityTest> history) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Quality Test History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showScheduleTestDialog(context),
                  child: const Text('Schedule Test'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (history.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    'No quality test history available',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              ...history.take(5).map((test) => _buildQualityTestItem(context, test)),

            if (history.length > 5)
              TextButton(
                onPressed: () => _showFullTestHistory(context, history),
                child: const Text('View All Tests'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityTestItem(BuildContext context, QualityTest test) {
    return ListTile(
      leading: Icon(
        Icons.science,
        color: _getQualityColor(test.result),
      ),
      title: Text('Quality Test'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Result: ${test.result}'),
          Text('Date: ${_formatDate(test.testDate)}'),
          if (test.notes.isNotEmpty)
            Text('Notes: ${test.notes}'),
        ],
      ),
      trailing: IconButton(
        icon: const Icon(Icons.visibility),
        onPressed: () => _showTestDetails(context, test),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return EnhancedErrorWidget(
      error: error,
      onRetry: () => ref.invalidate(waterQualityProvider),
      title: 'Failed to load quality data',
      message: 'Please check your connection and try again.',
    );
  }

  Color _getQualityColor(String quality) {
    switch (quality.toUpperCase()) {
      case 'EXCELLENT':
      case 'GOOD':
        return Colors.green;
      case 'FAIR':
        return Colors.orange;
      case 'POOR':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showScheduleTestDialog(BuildContext context) {
    // Implementation for scheduling quality test
  }

  void _showFullTestHistory(BuildContext context, List<QualityTest> history) {
    // Implementation for showing full test history
  }

  void _showTestDetails(BuildContext context, QualityTest test) {
    // Implementation for showing test details
  }
}

// Dialog Widgets
class WaterSystemSettingsDialog extends StatelessWidget {
  final String? propertyId;

  const WaterSystemSettingsDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Water System Settings'),
      content: const Text('Water system settings will be implemented here.'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

class AddWaterSystemDialog extends StatelessWidget {
  final String? propertyId;

  const AddWaterSystemDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Water System'),
      content: const Text('Add water system form will be implemented here.'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Add'),
        ),
      ],
    );
  }
}
