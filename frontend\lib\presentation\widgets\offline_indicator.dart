import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/service_locator.dart';

// Connectivity Provider
final connectivityProvider = StreamProvider<bool>((ref) {
  final connectivityManager = serviceLocator.connectivityManager;
  return connectivityManager.connectionStream;
});

// Current Connection Status Provider
final connectionStatusProvider = FutureProvider<bool>((ref) async {
  final connectivityManager = serviceLocator.connectivityManager;
  return connectivityManager.isConnected;
});

class OfflineIndicator extends ConsumerWidget {
  final bool showWhenOnline;
  final Duration animationDuration;
  final Color? offlineColor;
  final Color? onlineColor;

  const OfflineIndicator({
    super.key,
    this.showWhenOnline = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.offlineColor,
    this.onlineColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityAsyncValue = ref.watch(connectivityProvider);
    
    return connectivityAsyncValue.when(
      data: (isConnected) => _buildIndicator(context, isConnected),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => _buildIndicator(context, false),
    );
  }

  Widget _buildIndicator(BuildContext context, bool isConnected) {
    if (isConnected && !showWhenOnline) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: animationDuration,
      height: isConnected ? (showWhenOnline ? 32 : 0) : 32,
      child: Container(
        width: double.infinity,
        color: isConnected 
            ? (onlineColor ?? Colors.green)
            : (offlineColor ?? Colors.red),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isConnected ? Icons.wifi : Icons.wifi_off,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                isConnected ? 'Back online' : 'You\'re offline',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Floating offline indicator that appears as a snackbar-like widget
class FloatingOfflineIndicator extends ConsumerStatefulWidget {
  final Widget child;
  final Duration showDuration;
  final Duration animationDuration;

  const FloatingOfflineIndicator({
    super.key,
    required this.child,
    this.showDuration = const Duration(seconds: 3),
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  ConsumerState<FloatingOfflineIndicator> createState() => _FloatingOfflineIndicatorState();
}

class _FloatingOfflineIndicatorState extends ConsumerState<FloatingOfflineIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  bool _isVisible = false;
  bool _wasOffline = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<bool>>(connectivityProvider, (previous, next) {
      next.whenData((isConnected) {
        if (!isConnected && !_wasOffline) {
          // Just went offline
          _showIndicator(false);
          _wasOffline = true;
        } else if (isConnected && _wasOffline) {
          // Just came back online
          _showIndicator(true);
          _wasOffline = false;
          
          // Hide after duration
          Future.delayed(widget.showDuration, () {
            if (mounted) {
              _hideIndicator();
            }
          });
        }
      });
    });

    return Stack(
      children: [
        widget.child,
        
        if (_isVisible)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildFloatingIndicator(),
            ),
          ),
      ],
    );
  }

  Widget _buildFloatingIndicator() {
    final connectivityAsyncValue = ref.watch(connectivityProvider);
    
    return connectivityAsyncValue.when(
      data: (isConnected) => Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green : Colors.red,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isConnected ? Icons.wifi : Icons.wifi_off,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isConnected 
                    ? 'Connection restored' 
                    : 'No internet connection',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              onPressed: _hideIndicator,
              icon: const Icon(Icons.close, color: Colors.white, size: 18),
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  void _showIndicator(bool isOnline) {
    if (!mounted) return;
    
    setState(() {
      _isVisible = true;
    });
    _animationController.forward();
  }

  void _hideIndicator() {
    if (!mounted) return;
    
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isVisible = false;
        });
      }
    });
  }
}

/// Connection status widget that shows detailed connectivity information
class ConnectionStatusWidget extends ConsumerWidget {
  final bool showDetails;

  const ConnectionStatusWidget({
    super.key,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityAsyncValue = ref.watch(connectivityProvider);
    
    return connectivityAsyncValue.when(
      data: (isConnected) => _buildStatusWidget(context, isConnected),
      loading: () => _buildLoadingWidget(),
      error: (error, _) => _buildErrorWidget(error),
    );
  }

  Widget _buildStatusWidget(BuildContext context, bool isConnected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isConnected 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isConnected 
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isConnected ? Icons.wifi : Icons.wifi_off,
            color: isConnected ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            isConnected ? 'Online' : 'Offline',
            style: TextStyle(
              color: isConnected ? Colors.green : Colors.red,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (showDetails && !isConnected) ...[
            const SizedBox(width: 8),
            Text(
              '• Limited functionality',
              style: TextStyle(
                color: Colors.red.withValues(alpha: 0.7),
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
          const SizedBox(width: 6),
          Text(
            'Checking...',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning,
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            'Unknown',
            style: TextStyle(
              color: Colors.orange,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
