// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      role: json['role'] as String,
      assignedProperties: (json['assignedProperties'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      assignedPropertyIds: (json['assignedPropertyIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      assignedOfficeIds: (json['assignedOfficeIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      roleConfig: json['roleConfig'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool,
      avatar: json['avatar'] as String?,
      timezone: json['timezone'] as String,
      language: json['language'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      lastLogin: json['lastLogin'] as String?,
      permissions:
          UserPermissions.fromJson(json['permissions'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'role': instance.role,
      'assignedProperties': instance.assignedProperties,
      'assignedPropertyIds': instance.assignedPropertyIds,
      'assignedOfficeIds': instance.assignedOfficeIds,
      'roleConfig': instance.roleConfig,
      'isActive': instance.isActive,
      'avatar': instance.avatar,
      'timezone': instance.timezone,
      'language': instance.language,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'lastLogin': instance.lastLogin,
      'permissions': instance.permissions,
    };

UserPermissions _$UserPermissionsFromJson(Map<String, dynamic> json) =>
    UserPermissions(
      canViewDashboard: json['canViewDashboard'] as bool,
      canManageProperties: json['canManageProperties'] as bool,
      canManageOffice: json['canManageOffice'] as bool,
      canManageSecurity: json['canManageSecurity'] as bool,
      canManageMaintenance: json['canManageMaintenance'] as bool,
      canCreateMaintenance: json['canCreateMaintenance'] as bool,
      canManageUsers: json['canManageUsers'] as bool,
      canViewReports: json['canViewReports'] as bool,
      canExportData: json['canExportData'] as bool,
      canManageWaterSystems: json['canManageWaterSystems'] as bool,
      canManageElectricitySystems: json['canManageElectricitySystems'] as bool,
      canManageSecuritySystems: json['canManageSecuritySystems'] as bool,
      allowedScreens: (json['allowedScreens'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      allowedActions: (json['allowedActions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UserPermissionsToJson(UserPermissions instance) =>
    <String, dynamic>{
      'canViewDashboard': instance.canViewDashboard,
      'canManageProperties': instance.canManageProperties,
      'canManageOffice': instance.canManageOffice,
      'canManageSecurity': instance.canManageSecurity,
      'canManageMaintenance': instance.canManageMaintenance,
      'canCreateMaintenance': instance.canCreateMaintenance,
      'canManageUsers': instance.canManageUsers,
      'canViewReports': instance.canViewReports,
      'canExportData': instance.canExportData,
      'canManageWaterSystems': instance.canManageWaterSystems,
      'canManageElectricitySystems': instance.canManageElectricitySystems,
      'canManageSecuritySystems': instance.canManageSecuritySystems,
      'allowedScreens': instance.allowedScreens,
      'allowedActions': instance.allowedActions,
    };

AuthToken _$AuthTokenFromJson(Map<String, dynamic> json) => AuthToken(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      tokenType: json['tokenType'] as String,
    );

Map<String, dynamic> _$AuthTokenToJson(AuthToken instance) => <String, dynamic>{
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'expiresAt': instance.expiresAt.toIso8601String(),
      'tokenType': instance.tokenType,
    };

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      email: json['email'] as String,
      password: json['password'] as String,
      deviceId: json['deviceId'] as String?,
      deviceName: json['deviceName'] as String?,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
    };

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      token: AuthToken.fromJson(json['token'] as Map<String, dynamic>),
      message: json['message'] as String,
    );

Map<String, dynamic> _$LoginResponseToJson(LoginResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'token': instance.token,
      'message': instance.message,
    };
